# API Parameter Documentation

This document provides comprehensive documentation for all API parameters in the Byte Media v1 Go API.

## Table of Contents

1. [Endpoints Overview](#endpoints-overview)
2. [Property Detail Endpoints](#property-detail-endpoints)
3. [Rent Estimate Endpoint](#rent-estimate-endpoint)
4. [Search Endpoint](#search-endpoint)
5. [Parameter Types](#parameter-types)
6. [Request/Response Models](#requestresponse-models)

## Endpoints Overview

The API provides the following main endpoints:

- `/property` - Get complete property details
- `/propertyMinimal` - Get lightweight property details
- `/propertyImages` - Get property photos only
- `/rentEstimate` - Get rent estimates for properties
- `/search` - Search for properties with filters

## Property Detail Endpoints

### `/property`

Get complete property information including all available details.

**Parameters:**
- `id` (string, optional) - Property ID (Zillow ZPID)
- `url` (string, optional) - Property URL from Zillow
- `address` (string, optional) - Property address
- `listingPhotos` (string, optional) - Include photos ("true"/"false", default: "false")

**Requirements:**
- Must provide exactly one of: `id`, `url`, or `address`

**Example:**
```
GET /property?id=12345678&listingPhotos=true
GET /property?address=123%20Main%20St%20City%20State
```

### `/propertyMinimal`

Get lightweight property information with essential details only.

**Parameters:**
- `id` (string, optional) - Property ID (Zillow ZPID)
- `url` (string, optional) - Property URL from Zillow
- `address` (string, optional) - Property address
- `listingPhotos` (string, optional) - Include photos ("true"/"false", default: "false")

**Requirements:**
- Must provide exactly one of: `id`, `url`, or `address`

### `/propertyImages`

Get property photos and basic address information only.

**Parameters:**
- `id` (string, optional) - Property ID (Zillow ZPID)
- `url` (string, optional) - Property URL from Zillow
- `address` (string, optional) - Property address

**Requirements:**
- Must provide exactly one of: `id`, `url`, or `address`

## Rent Estimate Endpoint

### `/rentEstimate`

Get rental price estimates for a property based on comparable properties.

**Parameters:**
- `address` (string, required) - Property address
- `compPropStatus` (string, optional) - Filter comparable properties by status ("true"/"false")
- `distanceInMiles` (float, optional) - Search radius for comparable properties (default: 5.0)

**Example:**
```
GET /rentEstimate?address=123%20Main%20St%20City%20State&distanceInMiles=3.5
```

## Search Endpoint

### `/search`

Search for properties within specified geographic bounds with various filters.

**Required Parameters:**
- `neLat` (float, required) - Northeast boundary latitude
- `neLong` (float, required) - Northeast boundary longitude
- `swLat` (float, required) - Southwest boundary latitude
- `swLong` (float, required) - Southwest boundary longitude

**Optional Parameters:**
- `pagination` (int, optional) - Page number for results (default: 1)
- `zoom` (int, optional) - Map zoom level (default: 10)
- `isLotLand` (string, optional) - Filter for lot/land properties ("true"/"false")
- `priceMin` (int, optional) - Minimum price filter
- `priceMax` (int, optional) - Maximum price filter

**Example:**
```
GET /search?neLat=40.7831&neLong=-73.9712&swLat=40.7489&swLong=-74.0059&priceMin=500000&priceMax=1000000
```

## Parameter Types

### Geographic Coordinates

All coordinate parameters must be valid decimal degrees:
- Latitude: -90.0 to 90.0
- Longitude: -180.0 to 180.0

### Property Identifiers

- **Property ID**: Numeric string representing Zillow Property ID (ZPID)
- **Property URL**: Full Zillow property URL
- **Address**: Free-form text address (street, city, state format recommended)

### Price Ranges

- **Price values**: Integer values in USD
- **Distance values**: Float values in miles

### Boolean Parameters

Boolean parameters are passed as strings:
- `"true"` - Enable the filter/option
- `"false"` - Disable the filter/option
- Empty/missing - Default behavior (usually false)

## Request/Response Models

### Property Response Models

#### PropertyInfo (Complete Details)
Contains 200+ fields including:
- Basic info (address, price, bedrooms, bathrooms)
- Property details (year built, lot size, square footage)
- Market data (Zestimate, price history, tax information)
- School information and ratings
- Neighborhood statistics
- Property photos (if requested)
- MLS data and attribution

#### PropertyMinimalInfo (Lightweight)
Contains essential fields only:
- Address
- Price and Zestimate
- Basic property specs (beds, baths, square footage)
- Property ID and status
- Property photos (if requested)

#### ImagesOnly
Contains:
- Address
- Property ID
- Photo collection

### Search Response Models

#### Search Response
```json
{
  "listResults": [ListResult],
  "mapResults": [MapResult]
}
```

#### ListResult
Individual property in search results with:
- Property ID and basic info
- Address components
- Price and status
- Property specifications
- Thumbnail image
- Geographic coordinates

#### MapResult
Map display data with:
- Property location data
- Price and basic specs
- Thumbnail for map display

### Rent Estimate Response

#### RentZestimateResponse
```json
{
  "data": {
    "byAddress": {
      "geo": { "lat": float, "lon": float },
      "floorplans": [Floorplan],
      "marketSummary": MarketSummary,
      "similarFloorplans": SimilarFloorplans
    }
  }
}
```

## Error Responses

All endpoints return appropriate HTTP status codes:

- `400 Bad Request` - Invalid or missing required parameters
- `403 Forbidden` - Authentication/authorization issues
- `500 Internal Server Error` - Server processing errors

Error responses include descriptive error messages in the response body.

## Notes

- All responses are returned as formatted JSON with proper Content-Type headers
- Geographic coordinates use the WGS84 coordinate system
- Property IDs are Zillow's internal ZPID format
- Price values are in USD without currency symbols
- Date/time values follow ISO 8601 format where applicable
- Photo URLs in responses may be temporary and should be cached if needed